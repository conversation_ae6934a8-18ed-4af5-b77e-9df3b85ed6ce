// Jakarta timezone utilities for backend
export const JAKARTA_TIMEZONE = 'Asia/Jakarta';

/**
 * Get current time in Jakarta timezone
 */
export const getJakartaTime = (): Date => {
  return new Date(new Date().toLocaleString("en-US", { timeZone: JAKARTA_TIMEZONE }));
};

/**
 * Convert any date to Jakarta timezone
 */
export const toJakartaTime = (date: Date | string): Date => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return new Date(inputDate.toLocaleString("en-US", { timeZone: JAKARTA_TIMEZONE }));
};

/**
 * Get start of day in Jakarta timezone
 */
export const getStartOfDayJakarta = (date: Date | string = new Date()): Date => {
  const inputDate = toJakartaTime(date);
  inputDate.setHours(0, 0, 0, 0);
  return inputDate;
};

/**
 * Get end of day in Jakarta timezone
 */
export const getEndOfDayJakarta = (date: Date | string = new Date()): Date => {
  const inputDate = toJakartaTime(date);
  inputDate.setHours(23, 59, 59, 999);
  return inputDate;
};

/**
 * Check if a date is in the past (Jakarta timezone)
 */
export const isDatePastJakarta = (date: Date | string): boolean => {
  const inputDate = toJakartaTime(date);
  const now = getJakartaTime();
  return inputDate < now;
};

/**
 * Check if a date is in the future (Jakarta timezone)
 */
export const isDateFutureJakarta = (date: Date | string): boolean => {
  const inputDate = toJakartaTime(date);
  const now = getJakartaTime();
  return inputDate > now;
};

/**
 * Convert Jakarta time to UTC for database storage
 */
export const jakartaToUTC = (date: Date | string): Date => {
  const jakartaDate = toJakartaTime(date);
  // Jakarta is UTC+7
  return new Date(jakartaDate.getTime() - (7 * 60 * 60 * 1000));
};

/**
 * Convert UTC to Jakarta time for display
 */
export const utcToJakarta = (date: Date | string): Date => {
  const utcDate = typeof date === 'string' ? new Date(date) : date;
  // Jakarta is UTC+7
  return new Date(utcDate.getTime() + (7 * 60 * 60 * 1000));
};

/**
 * Format date for Jakarta timezone
 */
export const formatJakartaDate = (
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return inputDate.toLocaleString("en-US", {
    timeZone: JAKARTA_TIMEZONE,
    ...options
  });
};

/**
 * Format date for Jakarta timezone with Indonesian locale
 */
export const formatJakartaDateID = (
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return inputDate.toLocaleString("id-ID", {
    timeZone: JAKARTA_TIMEZONE,
    ...options
  });
};

/**
 * Get timezone offset for Jakarta (always +7)
 */
export const getJakartaOffset = (): number => {
  return 7; // Jakarta is UTC+7
};

/**
 * Create a date in Jakarta timezone from components
 */
export const createJakartaDate = (
  year: number,
  month: number,
  day: number,
  hour: number = 0,
  minute: number = 0,
  second: number = 0
): Date => {
  // Create date in Jakarta timezone
  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:${String(second).padStart(2, '0')}.000+07:00`;
  return new Date(dateStr);
};
