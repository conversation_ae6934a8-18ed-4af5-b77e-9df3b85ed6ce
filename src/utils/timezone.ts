// Jakarta timezone utilities
export const JAKARTA_TIMEZONE = 'Asia/Jakarta';

/**
 * Get current time in Jakarta timezone
 */
export const getJakartaTime = (): Date => {
  return new Date(new Date().toLocaleString("en-US", { timeZone: JAKARTA_TIMEZONE }));
};

/**
 * Convert any date to Jakarta timezone
 */
export const toJakartaTime = (date: Date | string): Date => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return new Date(inputDate.toLocaleString("en-US", { timeZone: JAKARTA_TIMEZONE }));
};

/**
 * Format date in Jakarta timezone
 */
export const formatJakartaDate = (
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return inputDate.toLocaleString("en-US", {
    timeZone: JAKARTA_TIMEZONE,
    ...options
  });
};

/**
 * Format date in Jakarta timezone with Indonesian locale
 */
export const formatJakartaDateID = (
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  return inputDate.toLocaleString("id-ID", {
    timeZone: JAKARTA_TIMEZONE,
    ...options
  });
};

/**
 * Get time difference in Jakarta timezone
 */
export const getTimeDifferenceJakarta = (
  startDate: Date | string,
  endDate: Date | string = new Date()
): number => {
  const start = toJakartaTime(startDate);
  const end = toJakartaTime(endDate);
  return end.getTime() - start.getTime();
};

/**
 * Check if a date is in the past (Jakarta timezone)
 */
export const isDatePastJakarta = (date: Date | string): boolean => {
  const inputDate = toJakartaTime(date);
  const now = getJakartaTime();
  return inputDate < now;
};

/**
 * Check if a date is in the future (Jakarta timezone)
 */
export const isDateFutureJakarta = (date: Date | string): boolean => {
  const inputDate = toJakartaTime(date);
  const now = getJakartaTime();
  return inputDate > now;
};

/**
 * Add time to a date in Jakarta timezone
 */
export const addTimeJakarta = (
  date: Date | string,
  milliseconds: number
): Date => {
  const inputDate = toJakartaTime(date);
  return new Date(inputDate.getTime() + milliseconds);
};

/**
 * Get start of day in Jakarta timezone
 */
export const getStartOfDayJakarta = (date: Date | string = new Date()): Date => {
  const inputDate = toJakartaTime(date);
  inputDate.setHours(0, 0, 0, 0);
  return inputDate;
};

/**
 * Get end of day in Jakarta timezone
 */
export const getEndOfDayJakarta = (date: Date | string = new Date()): Date => {
  const inputDate = toJakartaTime(date);
  inputDate.setHours(23, 59, 59, 999);
  return inputDate;
};

/**
 * Convert Jakarta time to UTC
 */
export const jakartaToUTC = (date: Date | string): Date => {
  const jakartaDate = toJakartaTime(date);
  // Jakarta is UTC+7
  return new Date(jakartaDate.getTime() - (7 * 60 * 60 * 1000));
};

/**
 * Convert UTC to Jakarta time
 */
export const utcToJakarta = (date: Date | string): Date => {
  const utcDate = typeof date === 'string' ? new Date(date) : date;
  // Jakarta is UTC+7
  return new Date(utcDate.getTime() + (7 * 60 * 60 * 1000));
};

// Re-export date-fns functions with Jakarta timezone support
import {
  formatDistanceToNow as dateFnsFormatDistanceToNow,
  format as dateFnsFormat,
  addDays as dateFnsAddDays
} from 'date-fns';

/**
 * Format distance to now in Jakarta timezone
 */
export const formatDistanceToNowJakarta = (
  date: Date | string,
  options?: Parameters<typeof dateFnsFormatDistanceToNow>[1]
): string => {
  const jakartaDate = toJakartaTime(date);
  const jakartaNow = getJakartaTime();
  return dateFnsFormatDistanceToNow(jakartaDate, {
    ...options,
    // Calculate relative to Jakarta time
    addSuffix: true,
    ...options
  });
};

/**
 * Format date in Jakarta timezone
 */
export const formatJakarta = (
  date: Date | string,
  formatStr: string,
  options?: Parameters<typeof dateFnsFormat>[2]
): string => {
  const jakartaDate = toJakartaTime(date);
  return dateFnsFormat(jakartaDate, formatStr, options);
};

/**
 * Add days to a date in Jakarta timezone
 */
export const addDaysJakarta = (
  date: Date | string,
  amount: number
): Date => {
  const jakartaDate = toJakartaTime(date);
  return dateFnsAddDays(jakartaDate, amount);
};
